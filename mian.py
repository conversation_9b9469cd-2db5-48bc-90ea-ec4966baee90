# -*- coding: utf-8 -*-
import sys, time, os
import logging, ntwork
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import json
import keyboard
import random

# 全局实例检查
_instance = None

# 默认配置结构
DEFAULT_CONFIG = {
    "monitored_rooms": [],  # 存储要监控的群ID列表
    "stability_settings": {
        "connection_check_interval": 300,  # 连接检查间隔（秒）
        "max_retry_count": 3,  # 最大重试次数
        "memory_cleanup_interval": 1800,  # 内存清理间隔（秒）
        "enable_heartbeat": True,  # 启用心跳检测
        "forward_delay_min": 3,  # 转发延迟最小值（秒）
        "forward_delay_max": 5   # 转发延迟最大值（秒）
    }
}


# 自定义日志处理器，将日志输出到GUI
class GuiHandler(logging.Handler):
    def __init__(self, text_widget):
        logging.Handler.__init__(self)
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)

        def append():
            self.text_widget.configure(state="normal")
            self.text_widget.insert(tk.END, msg + "\n")
            self.text_widget.see(tk.END)
            self.text_widget.configure(state="disabled")

        self.text_widget.after(0, append)


class WeChatGUI:
    def __init__(self):
        global _instance
        _instance = self

        self.root = tk.Tk()
        self.root.title("企业微信群监控")
        self.root.geometry("800x600")

        self.startup = None
        self.config = DEFAULT_CONFIG.copy()
        self.all_rooms = []  # 所有群列表
        self.monitoring_active = False  # 监控状态标志

        self.create_widgets()
        # 设置日志
        self.setup_logging()
        self.load_config()

        # 自动初始化企业微信
        self.auto_init_wechat()

        

    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
        )
        self.logger = logging.getLogger()

        # 添加GUI处理器
        gui_handler = GuiHandler(self.log_text)
        gui_handler.setFormatter(
            logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        )
        self.logger.addHandler(gui_handler)

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="5")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 顶部：登录信息区
        self.create_login_info_frame(main_frame)

        # 中间：群管理区
        self.create_room_management_frame(main_frame)

        # 底部：日志和控制区
        self.create_log_and_control_frame(main_frame)

        # 配置grid权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=0)  # 登录信息区固定高度
        main_frame.rowconfigure(1, weight=1)  # 群管理区可伸缩
        main_frame.rowconfigure(2, weight=1)  # 日志区可伸缩

    def create_login_info_frame(self, parent):
        """创建登录信息显示区域"""
        login_frame = ttk.LabelFrame(parent, text="登录信息", padding="5")
        login_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        # 创建信息显示标签
        info_frame = ttk.Frame(login_frame)
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # 状态标签
        ttk.Label(info_frame, text="状态:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.status_label = ttk.Label(info_frame, text="未登录", foreground="red")
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        # 用户ID标签
        ttk.Label(info_frame, text="用户ID:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.user_id_label = ttk.Label(info_frame, text="--")
        self.user_id_label.grid(row=0, column=3, sticky=tk.W, padx=(0, 20))

        # 用户名标签
        ttk.Label(info_frame, text="用户名:").grid(row=0, column=4, sticky=tk.W, padx=(0, 5))
        self.username_label = ttk.Label(info_frame, text="--")
        self.username_label.grid(row=0, column=5, sticky=tk.W)

        # 配置权重
        login_frame.columnconfigure(0, weight=1)
        info_frame.columnconfigure(5, weight=1)

    def create_room_management_frame(self, parent):
        """创建群管理区域"""
        room_frame = ttk.LabelFrame(parent, text="群管理", padding="5")
        room_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 所有群列表
        all_rooms_frame = ttk.LabelFrame(room_frame, text="所有群列表", padding="5")
        all_rooms_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # 刷新群列表按钮和状态
        button_frame = ttk.Frame(all_rooms_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        self.refresh_btn = ttk.Button(button_frame, text="刷新群列表", command=self.refresh_rooms_list)
        self.refresh_btn.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.refresh_status_label = ttk.Label(button_frame, text="", foreground="blue")
        self.refresh_status_label.grid(row=0, column=1, sticky=tk.W)

        button_frame.columnconfigure(1, weight=1)

        # 所有群的Listbox
        all_rooms_list_frame = ttk.Frame(all_rooms_frame)
        all_rooms_list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.all_rooms_listbox = tk.Listbox(all_rooms_list_frame, selectmode=tk.EXTENDED, height=10)
        all_rooms_scrollbar = ttk.Scrollbar(all_rooms_list_frame, orient=tk.VERTICAL, command=self.all_rooms_listbox.yview)
        self.all_rooms_listbox.configure(yscrollcommand=all_rooms_scrollbar.set)

        self.all_rooms_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        all_rooms_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 选择按钮
        select_btn_frame = ttk.Frame(all_rooms_frame)
        select_btn_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(select_btn_frame, text="添加选中", command=self.add_selected_rooms).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(select_btn_frame, text="全选", command=self.select_all_rooms).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(select_btn_frame, text="清空选择", command=self.clear_room_selection).grid(row=0, column=2)

        # 监控群列表
        monitored_rooms_frame = ttk.LabelFrame(room_frame, text="监控群列表", padding="5")
        monitored_rooms_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))

        # 监控群的Listbox
        monitored_rooms_list_frame = ttk.Frame(monitored_rooms_frame)
        monitored_rooms_list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.monitored_rooms_listbox = tk.Listbox(monitored_rooms_list_frame, selectmode=tk.EXTENDED, height=12)
        monitored_rooms_scrollbar = ttk.Scrollbar(monitored_rooms_list_frame, orient=tk.VERTICAL, command=self.monitored_rooms_listbox.yview)
        self.monitored_rooms_listbox.configure(yscrollcommand=monitored_rooms_scrollbar.set)

        self.monitored_rooms_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        monitored_rooms_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 移除按钮
        remove_btn_frame = ttk.Frame(monitored_rooms_frame)
        remove_btn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(remove_btn_frame, text="移除选中", command=self.remove_selected_rooms).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(remove_btn_frame, text="清空全部", command=self.clear_monitored_rooms).grid(row=0, column=1)

        # 配置权重
        room_frame.columnconfigure(0, weight=1)
        room_frame.columnconfigure(1, weight=1)
        room_frame.rowconfigure(0, weight=1)
        all_rooms_frame.columnconfigure(0, weight=1)
        all_rooms_frame.rowconfigure(1, weight=1)
        all_rooms_list_frame.columnconfigure(0, weight=1)
        all_rooms_list_frame.rowconfigure(0, weight=1)
        monitored_rooms_frame.columnconfigure(0, weight=1)
        monitored_rooms_frame.rowconfigure(0, weight=1)
        monitored_rooms_list_frame.columnconfigure(0, weight=1)
        monitored_rooms_list_frame.rowconfigure(0, weight=1)

    def create_log_and_control_frame(self, parent):
        """创建日志和控制区域"""
        # 日志显示区
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="5")
        log_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        self.log_text = scrolledtext.ScrolledText(log_frame, width=80, height=15)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.log_text.configure(state="disabled")

        # 控制按钮
        control_frame = ttk.Frame(parent, padding="5")
        control_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))

        self.start_btn = ttk.Button(
            control_frame, text="启动监控", command=self.start_monitoring
        )
        self.start_btn.grid(row=0, column=0, padx=(0, 5))

        self.stop_btn = ttk.Button(
            control_frame,
            text="停止监控",
            command=self.stop_monitoring,
            state="disabled",
        )
        self.stop_btn.grid(row=0, column=1, padx=(0, 5))

        ttk.Button(control_frame, text="加载配置", command=self.load_config).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(control_frame, text="保存配置", command=self.save_config).grid(row=0, column=3, padx=(0, 5))

        # 配置权重
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def auto_init_wechat(self):
        """自动初始化企业微信"""
        self.logger.info("正在自动初始化企业微信...")

        def init_thread():
            try:
                # 创建StartUp实例但不运行监控
                self.startup = StartUp(gui_instance=self, auto_run=False)

                # 在主线程中更新界面
                self.root.after(0, self.on_wechat_initialized)

            except Exception as e:
                self.logger.error(f"初始化企业微信失败: {e}")
                self.root.after(0, lambda: self.on_wechat_init_failed(str(e)))

        threading.Thread(target=init_thread, daemon=True).start()

    def on_wechat_initialized(self):
        """企业微信初始化完成后的回调"""
        self.logger.info("企业微信初始化完成，正在加载群列表...")
        self.all_rooms = self.startup.all_rooms
        self.update_all_rooms_display()

        # 更新登录信息显示
        if self.startup and hasattr(self.startup, 'user_id') and hasattr(self.startup, 'name'):
            self.logger.info(f"准备更新登录信息: user_id={self.startup.user_id}, name='{self.startup.name}'")
            self.update_login_info(self.startup.user_id, self.startup.name)
        else:
            self.logger.warning("无法获取登录信息: startup对象或属性不存在")

        self.logger.info("群列表加载完成")

    def update_login_info(self, user_id, username):
        """更新登录信息显示"""
        self.status_label.config(text="已登录", foreground="green")
        self.user_id_label.config(text=user_id or "--")

        # 处理用户名显示
        display_name = username if username else user_id if user_id else "--"
        self.username_label.config(text=display_name)

        self.logger.info(f"登录信息已更新: 用户名='{username}', 显示名='{display_name}', 用户ID='{user_id}'")

    def on_wechat_init_failed(self, error_msg):
        """企业微信初始化失败后的回调"""
        # 重置登录信息显示
        self.status_label.config(text="登录失败", foreground="red")
        self.user_id_label.config(text="--")
        self.username_label.config(text="--")
        messagebox.showerror("初始化失败", f"企业微信初始化失败：{error_msg}\n\n您可以稍后手动点击'获取群列表'重试。")

    def load_config(self):
        """加载配置文件"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                self.config = json.load(f)
                self.logger.info("配置文件加载成功")
        except FileNotFoundError:
            self.logger.info("配置文件不存在，使用默认配置")
            self.config = DEFAULT_CONFIG.copy()
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self.config = DEFAULT_CONFIG.copy()

        # 更新界面显示
        self.update_monitored_rooms_display()

    def save_config(self):
        """保存配置文件"""
        try:
            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            self.logger.info("配置保存成功")
            messagebox.showinfo("成功", "配置保存成功！")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")



    def update_monitored_rooms_display(self):
        """更新监控群显示"""
        self.monitored_rooms_listbox.delete(0, tk.END)
        for room_id in self.config.get("monitored_rooms", []):
            # 查找群名称
            room_name = self.get_room_name_by_id(room_id)
            display_text = f"{room_name} ({room_id})" if room_name else room_id
            self.monitored_rooms_listbox.insert(tk.END, display_text)

    def get_room_name_by_id(self, room_id):
        """根据群ID获取群名称"""
        for room in self.all_rooms:
            if room.get("conversation_id") == room_id:
                return room.get("nickname", "未知群名")
        return None



    def get_rooms_list(self):
        """获取群列表"""
        if not self.startup or not hasattr(self.startup, 'wechat'):
            # 如果没有startup实例，尝试自动初始化
            self.auto_init_wechat()
            return

        def get_rooms_thread():
            try:
                self.logger.info("正在获取群列表...")
                self.startup.get_all_rooms()
                self.all_rooms = self.startup.all_rooms

                # 在主线程中更新界面
                self.root.after(0, self.update_all_rooms_display)

                # 更新登录信息显示
                if hasattr(self.startup, 'user_id') and hasattr(self.startup, 'name'):
                    self.logger.info(f"手动获取群列表后更新登录信息: user_id={self.startup.user_id}, name='{self.startup.name}'")
                    self.root.after(0, lambda: self.update_login_info(self.startup.user_id, self.startup.name))
                else:
                    self.logger.warning("手动获取群列表时无法获取登录信息")

            except Exception as e:
                self.logger.error(f"获取群列表失败: {e}")

        threading.Thread(target=get_rooms_thread, daemon=True).start()

    def refresh_rooms_list(self):
        """刷新群列表（优化版本，避免卡顿）"""
        if not self.startup or not hasattr(self.startup, 'wechat'):
            messagebox.showwarning("警告", "请先启动监控或等待初始化完成")
            return

        # 禁用刷新按钮，防止重复点击
        self.refresh_btn.configure(state="disabled", text="刷新中...")
        self.refresh_status_label.config(text="正在获取群列表...")

        def refresh_thread():
            try:
                self.logger.info("开始快速刷新群列表...")

                # 使用快速模式获取群列表
                self.startup.get_all_rooms(fast_mode=True)
                self.all_rooms = self.startup.all_rooms

                # 在主线程中更新界面
                self.root.after(0, self.on_refresh_complete)

            except Exception as e:
                self.logger.error(f"刷新群列表失败: {e}")
                self.root.after(0, self.on_refresh_failed)

        threading.Thread(target=refresh_thread, daemon=True).start()

    def on_refresh_complete(self):
        """刷新完成的回调"""
        self.update_all_rooms_display()
        self.refresh_btn.configure(state="normal", text="刷新群列表")
        self.refresh_status_label.config(text=f"刷新完成，共 {len(self.all_rooms)} 个群")
        self.logger.info(f"群列表快速刷新完成，共获取 {len(self.all_rooms)} 个群")

        # 3秒后清除状态文字
        self.root.after(3000, lambda: self.refresh_status_label.config(text=""))

    def on_refresh_failed(self):
        """刷新失败的回调"""
        self.refresh_btn.configure(state="normal", text="刷新群列表")
        self.refresh_status_label.config(text="刷新失败", foreground="red")
        messagebox.showerror("错误", "刷新群列表失败，请查看日志")

        # 5秒后清除状态文字
        self.root.after(5000, lambda: self.refresh_status_label.config(text="", foreground="blue"))

    def update_all_rooms_display(self):
        """更新所有群列表显示"""
        self.all_rooms_listbox.delete(0, tk.END)
        for room in self.all_rooms:
            room_name = room.get("nickname", "未知群名")
            room_id = room.get("conversation_id", "")
            display_text = f"{room_name} ({room_id})"
            self.all_rooms_listbox.insert(tk.END, display_text)

        self.logger.info(f"群列表更新完成，共 {len(self.all_rooms)} 个群")

    def add_selected_rooms(self):
        """添加选中的群到监控列表"""
        selected_indices = self.all_rooms_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("警告", "请选择要添加的群")
            return

        added_count = 0
        for index in selected_indices:
            if index < len(self.all_rooms):
                room = self.all_rooms[index]
                room_id = room.get("conversation_id")

                # 检查是否已在监控列表中
                if room_id not in self.config["monitored_rooms"]:
                    self.config["monitored_rooms"].append(room_id)
                    added_count += 1

        if added_count > 0:
            self.update_monitored_rooms_display()
            self.logger.info(f"添加了 {added_count} 个群到监控列表")
        else:
            messagebox.showinfo("提示", "选中的群已在监控列表中")

    def select_all_rooms(self):
        """全选所有群"""
        self.all_rooms_listbox.select_set(0, tk.END)

    def clear_room_selection(self):
        """清空群选择"""
        self.all_rooms_listbox.selection_clear(0, tk.END)

    def remove_selected_rooms(self):
        """从监控列表中移除选中的群"""
        selected_indices = self.monitored_rooms_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("警告", "请选择要移除的群")
            return

        if messagebox.askyesno("确认", "确定要移除选中的群吗？"):
            # 从后往前删除，避免索引变化
            for index in reversed(selected_indices):
                if index < len(self.config["monitored_rooms"]):
                    del self.config["monitored_rooms"][index]

            self.update_monitored_rooms_display()
            self.logger.info("移除群成功")

    def clear_monitored_rooms(self):
        """清空所有监控群"""
        if messagebox.askyesno("确认", "确定要清空所有监控群吗？"):
            self.config["monitored_rooms"] = []
            self.update_monitored_rooms_display()
            self.logger.info("清空监控群列表")

    def start_monitoring(self):
        global _instance
        if _instance is not self:
            self.logger.error("检测到多个程序实例，请关闭其他窗口")
            return

        # 检查是否有监控群
        if not self.config.get("monitored_rooms"):
            messagebox.showwarning("警告", "请先选择要监控的群")
            return

        # 检查是否已登录
        if not self.startup:
            self.logger.error("微信未初始化，请重启程序")
            return

        try:
            self.startup.exit_flag = False
            login_info = self.startup.wechat.get_login_info()
            if not login_info:
                self.logger.error("微信未登录，请先登录")
                return
        except Exception as e:
            self.logger.error(f"获取登录状态失败: {e}")
            return

        self.save_config()  # 启动前保存当前配置

        # 设置监控参数
        try:
            # 确保退出标志为False
            self.startup.exit_flag = False

            # 注册消息回调
            self.startup.wechat.msg_register([ntwork.MT_RECV_LINK_CARD_MSG])(self.startup.on_recv_link_card_message)
            self.startup.wechat.msg_register([ntwork.MT_RECV_MIMI_APP_MSG])(self.startup.on_recv_miniapp_message)
            self.logger.info("消息回调注册成功")

            # 启动后台监控线程（用于连接状态检查等）
            if not hasattr(self, 'monitor_thread') or not self.monitor_thread.is_alive():
                self.monitor_thread = threading.Thread(target=self._background_monitor, daemon=True)
                self.monitor_thread.start()

            # 更新按钮状态
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
            self.monitoring_active = True
            self.logger.info("监控已启动")

        except Exception as e:
            self.logger.error(f"启动监控失败: {e}")
            self.logger.error("错误详情: ", exc_info=True)

    def stop_monitoring(self):
        if self.startup and self.monitoring_active:
            try:
                # 取消消息回调 - 注册空的回调函数
                self.startup.wechat.msg_register([ntwork.MT_RECV_LINK_CARD_MSG])(lambda x, y: None)
                self.startup.wechat.msg_register([ntwork.MT_RECV_MIMI_APP_MSG])(lambda x, y: None)

                # 设置退出标志，确保消息处理循环会检查这个标志
                self.startup.exit_flag = True

                self.logger.info("消息回调已取消")

            except Exception as e:
                self.logger.error(f"停止监控时发生错误: {e}")

        # 更新按钮状态
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.monitoring_active = False
        self.logger.info("监控已停止")

    def _background_monitor(self):
        """后台监控线程，用于连接状态检查等"""
        if not self.startup:
            return

        last_connection_check = time.time()
        last_memory_cleanup = time.time()
        loop_count = 0

        try:
            self.logger.info("后台监控线程已启动...")
            while self.monitoring_active and not self.startup.exit_flag:
                current_time = time.time()
                loop_count += 1

                # 每5分钟检查一次连接状态
                if current_time - last_connection_check > self.startup.connection_check_interval:
                    if not self.startup._check_connection_status():
                        self.logger.warning("连接状态异常，尝试处理...")
                        if not self.startup._handle_connection_error():
                            self.logger.error("连接恢复失败")
                            break
                    last_connection_check = current_time

                # 每30分钟进行一次内存清理
                if current_time - last_memory_cleanup > self.startup.memory_cleanup_interval:
                    self.startup._cleanup_memory()
                    last_memory_cleanup = current_time

                # 每1000次循环输出一次状态信息
                if loop_count % 1000 == 0:
                    self.logger.info(f"后台监控运行正常，循环次数: {loop_count}")

                time.sleep(1)

        except Exception as e:
            self.logger.error(f"后台监控线程发生异常: {e}")
        finally:
            self.logger.info("后台监控线程已退出")

    def run(self):
        self.root.mainloop()


# 修改StartUp类
class StartUp:
    def __init__(self, smart=True, gui_instance=None, auto_run=True):
        # 添加日志记录器
        self.logger = logging.getLogger()
        self.gui_instance = gui_instance
        self.auto_run = auto_run

        self.wechat = ntwork.WeWork()
        self.exit_flag = False
        self.last_heartbeat = time.time()
        self.current_retry_count = 0

        # 稳定性设置将在init_config后设置
        self.connection_check_interval = 300
        self.max_retry_count = 3
        self.memory_cleanup_interval = 1800
        self.enable_heartbeat = True
        self.forward_delay_min = 3
        self.forward_delay_max = 5

        # 初始化配置
        self.init_config()

        # 初始化微信
        self.init_wechat(smart)

        # 注册消息回调
        self.wechat.msg_register([ntwork.MT_RECV_LINK_CARD_MSG])(self.on_recv_link_card_message)
        self.wechat.msg_register([ntwork.MT_RECV_MIMI_APP_MSG])(self.on_recv_miniapp_message)

        # 监听掉线事件
        self.wechat.msg_register([ntwork.MT_RECV_WEWORK_QUIT_MSG])(self.handle_disconnect)

    def _interruptible_sleep(self, duration, description=""):
        """可中断的睡眠，避免长时间阻塞"""
        start_time = time.time()
        while time.time() - start_time < duration and not self.exit_flag:
            time.sleep(min(1, duration - (time.time() - start_time)))
            if description and int(time.time() - start_time) % 10 == 0:
                remaining = duration - (time.time() - start_time)
                if remaining > 0:
                    self.logger.info(f"{description}，剩余 {int(remaining)} 秒...")

    def _check_connection_status(self):
        """检查连接状态"""
        try:
            # 尝试获取登录信息来检查连接状态
            login_info = self.wechat.get_login_info()
            if login_info and login_info.get("user_id"):
                self.last_heartbeat = time.time()
                self.current_retry_count = 0
                return True
            else:
                self.logger.warning("连接状态检查失败：无法获取登录信息")
                return False
        except Exception as e:
            self.logger.warning(f"连接状态检查异常: {e}")
            return False

    def _handle_connection_error(self):
        """处理连接错误"""
        self.current_retry_count += 1
        if self.current_retry_count <= self.max_retry_count:
            self.logger.info(f"尝试重新连接 ({self.current_retry_count}/{self.max_retry_count})...")
            try:
                # 重新初始化连接
                self.wechat = ntwork.WeWork()
                self.init_wechat(smart=True)
                return True
            except Exception as e:
                self.logger.error(f"重连失败: {e}")
                return False
        else:
            self.logger.error("达到最大重试次数，停止重连")
            return False

    def init_config(self):
        # 从GUI获取配置
        if self.gui_instance:
            self.config = self.gui_instance.config
            self.logger.info(f"从GUI加载配置，监控群数量: {len(self.config.get('monitored_rooms', []))}")
        else:
            # 如果没有GUI实例，尝试直接读取配置文件
            try:
                with open("config.json", "r", encoding="utf-8") as f:
                    self.config = json.load(f)
            except Exception as e:
                self.logger.error(f"读取配置文件失败: {e}")
                self.config = DEFAULT_CONFIG.copy()

        # 应用稳定性设置
        stability_settings = self.config.get("stability_settings", {})
        self.connection_check_interval = stability_settings.get("connection_check_interval", 300)
        self.max_retry_count = stability_settings.get("max_retry_count", 3)
        self.memory_cleanup_interval = stability_settings.get("memory_cleanup_interval", 1800)
        self.enable_heartbeat = stability_settings.get("enable_heartbeat", True)
        self.forward_delay_min = stability_settings.get("forward_delay_min", 3)
        self.forward_delay_max = stability_settings.get("forward_delay_max", 5)

        self.logger.info(f"稳定性设置已应用: 连接检查间隔={self.connection_check_interval}s, 最大重试={self.max_retry_count}, 心跳检测={'开启' if self.enable_heartbeat else '关闭'}")

     

    def init_wechat(self, smart):
        self.wechat.open(smart=smart)
        self.logger.info("等待登录......")
        self.wechat.wait_login()
        login_info = self.wechat.get_login_info()
        self.user_id = login_info["user_id"]
        self.name = login_info.get("nickname", "")

        # 详细的登录信息调试
        self.logger.info(f"完整登录信息: {login_info}")
        self.logger.info(f"登录信息:>>>user_id:{self.user_id}>>>>>>>>name:{self.name}")

        # 如果nickname为空，尝试其他字段
        if not self.name:
            # 尝试其他可能的用户名字段
            possible_name_fields = ["name", "username", "display_name", "real_name"]
            for field in possible_name_fields:
                if field in login_info and login_info[field]:
                    self.name = login_info[field]
                    self.logger.info(f"使用字段 '{field}' 作为用户名: {self.name}")
                    break

            # 如果还是没有找到用户名，使用用户ID
            if not self.name:
                self.name = self.user_id
                self.logger.info(f"未找到用户名，使用用户ID作为显示名: {self.name}")

        self.logger.info("静默延迟60s，等待客户端刷新数据，请勿进行任何操作......")
        # 使用可中断的延迟，避免长时间阻塞
        self._interruptible_sleep(60, "等待客户端刷新数据")

        self.save_contact_info()
        self.get_all_rooms()
        self.setup_all_rooms_monitoring()

    def save_contact_info(self):
        try:
            self.contacts = self.wechat.get_external_contacts()
            self.rooms = self.wechat.get_rooms()

            if not self.contacts or not self.rooms:
                self.logger.error("获取contacts或rooms失败，程序退出")
                self.exit_program()
                return

            directory = os.path.join(os.getcwd(), "tmp")
            if not os.path.exists(directory):
                os.makedirs(directory)

            # 保存联系人信息
            self.save_json_file(
                os.path.join(directory, "wework_contacts.json"), self.contacts
            )
            self.save_json_file(
                os.path.join(directory, "wework_rooms.json"), self.rooms
            )

            # 保存群成员信息
            room_members = {}
            for room in self.rooms["room_list"]:
                room_wxid = room["conversation_id"]
                room_members[room_wxid] = self.wechat.get_room_members(room_wxid)

            self.save_json_file(
                os.path.join(directory, "wework_room_members.json"), room_members
            )
            self.logger.info("wework程序初始化完成")
        except Exception as e:
            self.logger.error(f"保存联系人信息失败: {e}")
            self.exit_program()

    def save_json_file(self, filepath, data):
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

    def get_all_rooms(self, fast_mode=False):
        """获取所有群列表，参考cankao.py中的refresh_room_list方法"""
        try:
            if fast_mode:
                self.logger.info("正在快速刷新群列表（仅获取前100个群）...")
            else:
                self.logger.info("正在获取所有群列表...")

            # 添加重试机制
            max_retries = 2 if fast_mode else 3  # 快速模式减少重试次数
            self.all_rooms = []
            total_rooms = 0
            current_page = 1
            room_page_size = 100 if fast_mode else 50  # 快速模式增加页大小

            while True:
                success = False
                for i in range(max_retries):
                    try:
                        # 带分页参数获取群列表
                        rooms = self.wechat.get_rooms(page_num=current_page, page_size=room_page_size)
                        if rooms and 'room_list' in rooms and rooms['room_list']:
                            room_count = len(rooms['room_list'])
                            self.logger.info(f"成功获取第 {current_page} 页群列表，有 {room_count} 个群")
                            self.all_rooms.extend(rooms['room_list'])
                            total_rooms += room_count
                            success = True
                            break
                        else:
                            # 如果返回空列表，可能已经获取完所有群
                            if current_page > 1:
                                self.logger.info(f"第 {current_page} 页没有更多群，获取完成")
                                success = True
                                break
                            else:
                                self.logger.warning(f"第 {current_page} 页获取群列表为空，尝试重试... ({i + 1}/{max_retries})")
                                self._interruptible_sleep(1)  # 减少重试延迟
                    except Exception as e:
                        self.logger.warning(f"获取群列表第 {current_page} 页失败: {e}，尝试重试... ({i + 1}/{max_retries})")
                        self._interruptible_sleep(1)  # 减少重试延迟

                # 如果当前页获取失败且是第一页，则无法继续
                if not success and current_page == 1:
                    self.logger.error("无法获取群列表，请检查企业微信状态")
                    return

                # 如果当前页获取的群数量少于页大小，说明已经没有更多群了
                if success and (len(rooms.get('room_list', [])) < room_page_size):
                    self.logger.info(f"已获取所有群，总共 {total_rooms} 个群")
                    break

                # 快速模式只获取第一页
                if fast_mode and current_page >= 1:
                    self.logger.info(f"快速模式完成，获取了 {total_rooms} 个群")
                    break

                # 继续获取下一页
                current_page += 1

            self.logger.info(f"成功获取所有群列表，共 {len(self.all_rooms)} 个群")

        except Exception as e:
            self.logger.error(f"获取所有群列表失败: {str(e)}")
            self.all_rooms = []

    def on_recv_link_card_message(self, wechat_instance: ntwork.WeWork, message):
        try:
            # 更新心跳时间
            self.last_heartbeat = time.time()

            # 添加随机延迟，避免频繁操作
            self._interruptible_sleep(random.uniform(0.5, 2))
    
            if self.exit_flag:
                return

            data = message["data"]
            self.logger.info(f"link_card_data: {data}")
            conversation_id = data.get("conversation_id", "").strip()
            title = data.get("title", "").strip()
            image_url = data.get("image_url", "").strip()
            desc = data.get("desc", "").strip()
            url = data.get("url", "").strip()

            from_wxid = data.get("sender")
            sender_name = data.get("sender_name")

            try:
                my_wxid = wechat_instance.get_login_info()["user_id"]
            except Exception as e:
                self.logger.error(f"获取登录信息失败: {e}")
                return

            is_group = True if conversation_id.startswith("R:") else False

            # 只处理群聊消息，且不是自己发送的消息
            # if from_wxid == my_wxid or not is_group:
            #     return

            # 检查是否是监控的群
            monitored_rooms = self.config.get("monitored_rooms", [])
            if conversation_id not in monitored_rooms:
                return

            # 转发到其他群
            forwarded_count = 0

            if conversation_id not in self.all_rooms:
                self.all_rooms.append(conversation_id)
            for room in self.all_rooms:
                if self.exit_flag:
                    break
                room_id = room.get("conversation_id")
                if room_id and room_id not in monitored_rooms:
                    try:
                        self.logger.info(f"转发链接卡片到群: {room_id}")
                        self._interruptible_sleep(random.uniform(self.forward_delay_min, self.forward_delay_max))
                        self.logger.info(f"是否退出标志位？{str(self.exit_flag)}")
                        if not self.exit_flag:
                            wechat_instance.send_link_card(
                                conversation_id=room_id,
                                title=title,
                                desc=desc,
                                url=url,
                                image_url=image_url
                            )

                            forwarded_count += 1
                    except Exception as e:
                        self.logger.error(f"转发链接卡片到群 {room_id} 失败: {e}")
                        continue

            self.logger.info(f"收到监控群 {conversation_id} 中 {sender_name} 的链接卡片消息: {title}，已转发到 {forwarded_count} 个群")

        except Exception as e:
            self.logger.error(f"处理链接卡片消息时发生错误: {e}",exc_info=True)

            # 检查是否是连接问题
            if not self._check_connection_status():
                self._handle_connection_error()

    def handle_disconnect(self, wechat_instance: ntwork.WeWork, message):
        """处理掉线事件"""
        try:
            self.logger.warning("检测到企业微信掉线事件")

            # 更新GUI状态
            if self.gui_instance:
                self.gui_instance.root.after(0, self._update_disconnect_status)

            # 尝试重连
            self.logger.info("开始自动重连...")
            if self._handle_connection_error():
                self.logger.info("自动重连成功")
                if self.gui_instance:
                    self.gui_instance.root.after(0, self._update_reconnect_status)
            else:
                self.logger.error("自动重连失败")
                if self.gui_instance:
                    self.gui_instance.root.after(0, self._show_disconnect_error)

        except Exception as e:
            self.logger.error(f"处理掉线事件时发生错误: {e}")

    def _update_disconnect_status(self):
        """更新掉线状态到GUI"""
        if self.gui_instance:
            self.gui_instance.status_label.config(text="连接断开", foreground="orange")
            self.gui_instance.logger.warning("企业微信连接已断开，正在尝试重连...")

    def _update_reconnect_status(self):
        """更新重连成功状态到GUI"""
        if self.gui_instance:
            self.gui_instance.status_label.config(text="已登录", foreground="green")
            self.gui_instance.logger.info("企业微信重连成功")

    def _show_disconnect_error(self):
        """显示掉线错误"""
        if self.gui_instance:
            self.gui_instance.status_label.config(text="连接失败", foreground="red")
            messagebox.showerror("连接错误", "企业微信连接断开且重连失败，请手动重启监控")

    def on_recv_miniapp_message(self, wechat_instance: ntwork.WeWork, message):
        try:
            # 更新心跳时间
            self.last_heartbeat = time.time()

            # 添加随机延迟，避免频繁操作
            self._interruptible_sleep(random.uniform(0.5, 2))

            if self.exit_flag:
                return

            data = message["data"]
            self.logger.info(f"miniapp_data: {data}")

            appicon = data.get("appicon", "").strip()
            appid = data.get("appid", "").strip()
            appname = data.get("appname", "").strip()
            page_path = data.get("page_path", "").strip()
            title = data.get("title", "").strip()
            username = data.get("username", "").strip()

            # 安全获取cdn数据
            cdn_data = data.get('cdn', {})
            aes_key = cdn_data.get("aes_key", "").strip()
            file_id = cdn_data.get("file_id", "").strip()
            size = int(cdn_data.get("size", 0))

            conversation_id = data.get("conversation_id", "").strip()
            from_wxid = message["data"].get("sender")
            sender_name = message["data"].get("sender_name")

            try:
                my_wxid = wechat_instance.get_login_info()["user_id"]
            except Exception as e:
                self.logger.error(f"获取登录信息失败: {e}")
                return

            is_group = True if conversation_id.startswith("R:") else False

            # 只处理群聊消息，且不是自己发送的消息
            # if from_wxid == my_wxid or not is_group:
            #     return

            # 检查是否是监控的群
            monitored_rooms = self.config.get("monitored_rooms", [])
            if conversation_id not in monitored_rooms:
                return

            # 转发到其他群
            forwarded_count = 0
       
            if conversation_id not in self.all_rooms:
                self.all_rooms.append(conversation_id)
            for room in self.all_rooms:
                if self.exit_flag:
                    break

                room_id=room.get('conversation_id')
                if room_id and room_id not in monitored_rooms:
                    try:
                        self.logger.info(f"转发小程序到群: {room_id}")
                        self._interruptible_sleep(random.uniform(self.forward_delay_min, self.forward_delay_max))
                        self.logger.info(f"是否退出标志位？{str(self.exit_flag)}")
                        if not self.exit_flag:
                            wechat_instance.send_miniapp(
                                aes_key=aes_key,
                                file_id=file_id,
                                size=size,
                                appicon=appicon,
                                appid=appid,
                                appname=appname,
                                conversation_id=room_id,
                                page_path=page_path,
                                title=title,
                                username=username
                            )

                            forwarded_count += 1
                    except Exception as e:
                        self.logger.error(f"转发小程序到群 {room} 失败: {e}",exc_info=True)
                        continue
           
            self.logger.info(f"收到监控群 {conversation_id} 中 {sender_name} 的小程序消息: {appname}，已转发到 {forwarded_count} 个群")

        except Exception as e:
            self.logger.error(f"处理小程序消息时发生错误: {e}",exc_info=True)
            # 检查是否是连接问题
            if not self._check_connection_status():
                self._handle_connection_error()

    def setup_all_rooms_monitoring(self):
        """设置监控选定的群"""
        monitored_rooms = self.config.get("monitored_rooms", [])
        if monitored_rooms:
            self.logger.info(f"设置监控选定的群，共 {len(monitored_rooms)} 个群")

            # 打印监控群的信息
            for room_id in monitored_rooms:
                room_name = "未知群名"
                if hasattr(self, 'all_rooms') and self.all_rooms:
                    for room in self.all_rooms:
                        if room.get('conversation_id') == room_id:
                            room_name = room.get('nickname', '未知群名')
                            break
                self.logger.info(f"监控群: {room_name} - {room_id}")
        else:
            self.logger.warning("未配置监控群，将不会处理任何群消息")

    def exit_program(self):
        self.logger.info("正在退出程序...")
        self.exit_flag = True

        try:
            # 清理键盘钩子
            keyboard.unhook_all()
            self.logger.info("键盘钩子已清理")
        except Exception as e:
            self.logger.warning(f"清理键盘钩子失败: {e}")

        try:
            # 清理微信连接
            if hasattr(self, 'wechat') and self.wechat:
                ntwork.exit_()
                self.logger.info("微信连接已关闭")
        except Exception as e:
            self.logger.warning(f"关闭微信连接失败: {e}")

        try:
            # 内存清理
            self._cleanup_memory()
        except Exception as e:
            self.logger.warning(f"最终内存清理失败: {e}")

        self.logger.info("程序退出完成")
        sys.exit(0)

    def run(self):
        # 如果不是自动运行模式，直接返回
        if not self.auto_run:
            return

        def exit_on_ctrl_q(event):
            if (
                event.event_type == "down"
                and event.name == "q"
                and keyboard.is_pressed("ctrl")
            ):
                self.exit_program()

        keyboard.on_press(exit_on_ctrl_q)

        # 运行状态监控
        last_connection_check = time.time()
        last_memory_cleanup = time.time()
        loop_count = 0

        try:
            self.logger.info("开始运行监控循环...")
            while not self.exit_flag:
                current_time = time.time()
                loop_count += 1

                # 每5分钟检查一次连接状态
                if current_time - last_connection_check > self.connection_check_interval:
                    self.logger.info("执行连接状态检查...")
                    if not self._check_connection_status():
                        self.logger.warning("连接状态异常，尝试处理...")
                        if not self._handle_connection_error():
                            self.logger.error("连接恢复失败，退出程序")
                            break
                    last_connection_check = current_time

                # 定期进行内存清理
                if current_time - last_memory_cleanup > self.memory_cleanup_interval:
                    self.logger.info("执行内存清理...")
                    self._cleanup_memory()
                    last_memory_cleanup = current_time

                # 每1000次循环输出一次状态信息
                if loop_count % 1000 == 0:
                    uptime = current_time - self.last_heartbeat
                    self.logger.info(f"运行状态正常，循环次数: {loop_count}，上次心跳: {uptime:.1f}秒前")

                time.sleep(1)

        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号")
            self.exit_program()
        except Exception as e:
            self.logger.error(f"运行循环发生异常: {e}")
            self.exit_program()

    def _cleanup_memory(self):
        """内存清理"""
        try:
            import gc
            gc.collect()
            self.logger.info("内存清理完成")
        except Exception as e:
            self.logger.warning(f"内存清理失败: {e}")


if __name__ == "__main__":
    gui = WeChatGUI()
    gui.run()
